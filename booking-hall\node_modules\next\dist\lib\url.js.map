{"version": 3, "sources": ["../../src/lib/url.ts"], "sourcesContent": ["import { NEXT_RSC_UNION_QUERY } from '../client/components/app-router-headers'\n\nconst DUMMY_ORIGIN = 'http://n'\n\nexport function isFullStringUrl(url: string) {\n  return /https?:\\/\\//.test(url)\n}\n\nexport function parseUrl(url: string): URL | undefined {\n  let parsed = undefined\n  try {\n    parsed = new URL(url, DUMMY_ORIGIN)\n  } catch {}\n  return parsed\n}\n\nexport function stripNextRscUnionQuery(relativeUrl: string): string {\n  const urlInstance = new URL(relativeUrl, DUMMY_ORIGIN)\n  urlInstance.searchParams.delete(NEXT_RSC_UNION_QUERY)\n\n  return urlInstance.pathname + urlInstance.search\n}\n"], "names": ["isFullStringUrl", "parseUrl", "stripNextRscUnionQuery", "DUMMY_ORIGIN", "url", "test", "parsed", "undefined", "URL", "relativeUrl", "urlInstance", "searchParams", "delete", "NEXT_RSC_UNION_QUERY", "pathname", "search"], "mappings": ";;;;;;;;;;;;;;;;IAIgBA,eAAe;eAAfA;;IAIAC,QAAQ;eAARA;;IAQAC,sBAAsB;eAAtBA;;;kCAhBqB;AAErC,MAAMC,eAAe;AAEd,SAASH,gBAAgBI,GAAW;IACzC,OAAO,cAAcC,IAAI,CAACD;AAC5B;AAEO,SAASH,SAASG,GAAW;IAClC,IAAIE,SAASC;IACb,IAAI;QACFD,SAAS,IAAIE,IAAIJ,KAAKD;IACxB,EAAE,OAAM,CAAC;IACT,OAAOG;AACT;AAEO,SAASJ,uBAAuBO,WAAmB;IACxD,MAAMC,cAAc,IAAIF,IAAIC,aAAaN;IACzCO,YAAYC,YAAY,CAACC,MAAM,CAACC,sCAAoB;IAEpD,OAAOH,YAAYI,QAAQ,GAAGJ,YAAYK,MAAM;AAClD"}